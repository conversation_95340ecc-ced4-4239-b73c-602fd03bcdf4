import * as React from 'react';
import {
  render, screen, fireEvent, waitFor, act,
} from '@testing-library/react';
import '@testing-library/jest-dom';
import TeamsSettingModal, { ITeamsModalProps } from './TeamsSettingModal';
import useUserChatsAndChannelsAccessor from '../../../../hooks/accessors/useUserChatsAndChannelsAccessor';
import useComponentInitUtility from '../../../../hooks/utilities/useComponentInitUtility';
import useMessageToasterBehavior from '../../../../hooks/behaviors/useMessageToasterBehavior';
import useTeamsSettingData from '../../../../hooks/features/useTeamsSettingData';
import useIndexedDbAccessor from '../../../../hooks/accessors/useIndexedDbAccessor';
import mockMatchMedia from '../../../../mocks/match-media';

// window.matchMediaのモック設定
mockMatchMedia();

// ISelectedItemの型定義
interface ISelectedItem {
  id: string;
  name: string;
  type: 'チャット' | 'チャネル';
  chatType: 'oneOnOne' | 'group' | 'meeting' | 'channel';
  teamId?: string;
  countId?: number;
}

// モック設定
jest.mock('../../../../utilities/environment');
jest.mock('../../../../hooks/accessors/useUserChatsAndChannelsAccessor');
jest.mock('../../../../hooks/utilities/useComponentInitUtility');
jest.mock('../../../../hooks/behaviors/useMessageToasterBehavior');
jest.mock('../../../../hooks/features/useTeamsSettingData');
jest.mock('../../../../hooks/accessors/useIndexedDbAccessor');

// react-custom-scrollbars-2のモック
interface ScrollFrameValues {
  scrollTop: number;
  scrollHeight: number;
  clientHeight: number;
}

interface ScrollbarsProps {
  children: React.ReactNode;
  // eslint-disable-next-line react/require-default-props
  onScrollFrame?: (values: ScrollFrameValues) => void;
  [key: string]: unknown;
}

jest.mock('react-custom-scrollbars-2', () => ({
  Scrollbars: ({ children, onScrollFrame, ...props }: ScrollbarsProps) => (
    <div
      data-testid="scrollbars"
      {...props}
      onScroll={(e: React.UIEvent<HTMLDivElement>) => {
        const target = e.target as HTMLDivElement;
        if (onScrollFrame) {
          onScrollFrame({
            scrollTop: target.scrollTop || 0,
            scrollHeight: target.scrollHeight || 1000,
            clientHeight: target.clientHeight || 500,
          });
        }
      }}
    >
      {children}
    </div>
  ),
}));

// ModalCardTopのモック
jest.mock('../../../commons/molecules/modal-card-top/ModalCardTop', () => ({
  __esModule: true,
  default: ({ onClickClose }: { onClickClose: () => void }) => (
    <button type="button" onClick={onClickClose} data-testid="modal-card-top-close">
      Close
    </button>
  ),
}));

// MessageToasterのモック
jest.mock('../../../commons/molecules/message-toaster/MessageToaster', () => ({
  __esModule: true,
  ToasterMessage: {
    BLANK: '',
    MAX_TEAMS_SELECTION: 'max-teams-selection',
  },
  default: ({ isActive, messageType }: { isActive: boolean; messageType: string }) => (
    isActive ? <div data-testid="message-toaster">{messageType}</div> : null
  ),
}));

jest.mock('./TeamsSettingTabs', () => ({
  __esModule: true,
  TeamsSettingTabType: {
    CHAT: 'チャット',
    CHANNEL: 'チャネル',
  },
  default: ({
    activeTab,
    onTabChange,
    chatCount,
    channelCount,
  }: {
    activeTab: 'チャット' | 'チャネル';
    onTabChange: (tab: 'チャット' | 'チャネル') => void;
    chatCount: number;
    channelCount: number;
  }) => (
    <div data-testid="teams-setting-tabs">
      <button type="button" onClick={() => onTabChange('チャット')}>
        チャット (
        {chatCount}
        )
      </button>
      <button type="button" onClick={() => onTabChange('チャネル')}>
        チャネル (
        {channelCount}
        )
      </button>
      <span>
        Active:
        {activeTab}
      </span>
    </div>
  ),
}));

jest.mock('./SelectedItemsList', () => ({
  __esModule: true,
  default: ({
    selectedItems,
    onRemoveItem,
  }: {
    selectedItems: Set<ISelectedItem>;
    onRemoveItem: (item: ISelectedItem) => void;
  }) => (
    <div data-testid="selected-items-list">
      {[...selectedItems].map((item) => (
        <div key={item.id}>
          Selected:
          {' '}
          {item.id}
          <button type="button" onClick={() => onRemoveItem(item)}>Remove</button>
        </div>
      ))}
    </div>
  ),
}));

// eslint-disable-next-line max-len
const mockUseUserChatsAndChannelsAccessor = useUserChatsAndChannelsAccessor as jest.MockedFunction<typeof useUserChatsAndChannelsAccessor>;
// eslint-disable-next-line max-len
const mockUseComponentInitUtility = useComponentInitUtility as jest.MockedFunction<typeof useComponentInitUtility>;
// eslint-disable-next-line max-len
const mockUseMessageToasterBehavior = useMessageToasterBehavior as jest.MockedFunction<typeof useMessageToasterBehavior>;
// eslint-disable-next-line max-len
const mockUseTeamsSettingData = useTeamsSettingData as jest.MockedFunction<typeof useTeamsSettingData>;
// eslint-disable-next-line max-len
const mockUseIndexedDbAccessor = useIndexedDbAccessor as jest.MockedFunction<typeof useIndexedDbAccessor>;

describe('TeamsSettingModal', () => {
  const mockPostTeamsChatsApi = jest.fn();
  const mockGetTeamsChatsApi = jest.fn();
  const mockDeleteTeamsChatsApi = jest.fn();
  const mockFetchUserChatsAndChannelsPaginated = jest.fn();
  const mockOnClose = jest.fn();
  const mockExtendPopupTimer = jest.fn();
  const mockEventReporter = jest.fn();
  const mockOpenDB = jest.fn();
  const mockSaveSelectedItems = jest.fn();
  const mockSetSavedItems = jest.fn();
  const mockLoadMoreChats = jest.fn();

  const defaultProps: ITeamsModalProps = {
    open: true,
    onClose: mockOnClose,
    useTeamsChatsApiAccessorReturn: {
      postTeamsChatsApi: mockPostTeamsChatsApi,
      getTeamsChatsApi: mockGetTeamsChatsApi,
      deleteTeamsChatsApi: mockDeleteTeamsChatsApi,
    },
    eventReporter: mockEventReporter,
  };

  const mockChatItems = [
    {
      id: 'chat1',
      name: 'テストチャット1',
      type: 'チャット' as const,
      chatType: 'oneOnOne' as const,
      teamId: undefined,
    },
    {
      id: 'chat2',
      name: 'プロジェクトチャット',
      type: 'チャット' as const,
      chatType: 'group' as const,
      teamId: undefined,
    },
    {
      id: 'chat3',
      name: 'テストチャット3',
      type: 'チャット' as const,
      chatType: 'oneOnOne' as const,
      teamId: undefined,
    },
    {
      id: 'chat4',
      name: 'テストチャット4',
      type: 'チャット' as const,
      chatType: 'oneOnOne' as const,
      teamId: undefined,
    },
    {
      id: 'chat5',
      name: 'テストチャット5',
      type: 'チャット' as const,
      chatType: 'oneOnOne' as const,
      teamId: undefined,
    },
    {
      id: 'chat6',
      name: 'テストチャット6',
      type: 'チャット' as const,
      chatType: 'oneOnOne' as const,
      teamId: undefined,
    },
    {
      id: 'chat7',
      name: 'テストチャット7',
      type: 'チャット' as const,
      chatType: 'oneOnOne' as const,
      teamId: undefined,
    },
    {
      id: 'chat8',
      name: 'テストチャット8',
      type: 'チャット' as const,
      chatType: 'oneOnOne' as const,
      teamId: undefined,
    },
    {
      id: 'chat9',
      name: 'テストチャット9',
      type: 'チャット' as const,
      chatType: 'oneOnOne' as const,
      teamId: undefined,
    },
    {
      id: 'chat10',
      name: 'テストチャット10',
      type: 'チャット' as const,
      chatType: 'oneOnOne' as const,
      teamId: undefined,
    },
    {
      id: 'chat11',
      name: 'テストチャット11',
      type: 'チャット' as const,
      chatType: 'oneOnOne' as const,
      teamId: undefined,
    },
    {
      id: 'channel1',
      name: 'テストチーム - 一般',
      type: 'チャネル' as const,
      chatType: 'channel' as const,
      teamId: 'team1',
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();

    // useComponentInitUtility のモック設定
    const mockCallbacks = new Map([
      ['graph', jest.fn().mockResolvedValue('mock-token')],
    ]);

    mockUseComponentInitUtility.mockReturnValue([
      { current: false }, // initRef
      [jest.fn(), jest.fn()], // lifecycle functions
      mockCallbacks, // callbacks map
      undefined, // 4th element
      undefined, // 5th element
      undefined, // 6th element
      undefined, // 7th element
      undefined, // 8th element
    ] as unknown as ReturnType<typeof useComponentInitUtility>);

    // useUserChatsAndChannelsAccessorのモック設定
    mockUseUserChatsAndChannelsAccessor.mockReturnValue({
      fetchUserChatsAndChannelsPaginated: mockFetchUserChatsAndChannelsPaginated,
      fetchUserTeamsAndChannelsPaginated: undefined,
      fetchUserChatsTotalCount: jest.fn().mockResolvedValue(100),
      fetchUserTeamsTotalCount: undefined,
      isLoading: false,
      error: null,
    });

    // useMessageToasterBehaviorのモック設定
    mockUseMessageToasterBehavior.mockReturnValue([
      false,
      '',
      mockExtendPopupTimer,
    ]);

    // useIndexedDbAccessorのモック設定
    mockUseIndexedDbAccessor.mockReturnValue([mockOpenDB]);

    // useTeamsSettingDataのモック設定
    mockUseTeamsSettingData.mockReturnValue({
      allChatItems: mockChatItems,
      savedSelectedItems: new Set(),
      savedItems: new Set(),
      isLoadingSavedItems: false,
      hasMoreChats: false,
      nextPageToken: undefined,
      isLoadingMore: false,
      isLoadingTotalCount: false,
      loadSavedItems: jest.fn(),
      saveSelectedItems: mockSaveSelectedItems,
      loadMoreChats: mockLoadMoreChats,
      setAllChatItems: jest.fn(),
      setSavedItems: mockSetSavedItems,
    });

    // API関数のデフォルト設定
    mockFetchUserChatsAndChannelsPaginated.mockResolvedValue({
      items: mockChatItems,
      hasMore: false,
      nextPageToken: undefined,
    });
    mockGetTeamsChatsApi.mockResolvedValue([]);
    mockPostTeamsChatsApi.mockResolvedValue(undefined);
    mockDeleteTeamsChatsApi.mockResolvedValue(undefined);
    mockSaveSelectedItems.mockResolvedValue(undefined);
  });

  describe('基本的なレンダリングテスト', () => {
    it('モーダルが正常にレンダリングされる', async () => {
      render(<TeamsSettingModal {...defaultProps} />);

      expect(screen.getByText('Teams設定')).toBeInTheDocument();
      expect(screen.getByText('検索対象を選択できます。最大10件まで選択可能です。')).toBeInTheDocument();

      // データが読み込まれるまで待機
      await waitFor(() => {
        expect(screen.getByText('テストチャット1')).toBeInTheDocument();
      });
    });

    it('openがfalseの場合、モーダルが非表示になる', () => {
      const { container } = render(<TeamsSettingModal {...defaultProps} open={false} />);
      expect(container.firstChild).not.toHaveClass('is-open');
    });

    it('openがtrueの場合、モーダルが表示される', () => {
      const { container } = render(<TeamsSettingModal {...defaultProps} open />);
      expect(container.firstChild).toHaveClass('is-open');
    });
  });

  describe('データ取得テスト', () => {
    it('モーダルが開かれた時にuseTeamsSettingDataが呼ばれる', async () => {
      render(<TeamsSettingModal {...defaultProps} />);

      await waitFor(() => {
        expect(mockUseTeamsSettingData).toHaveBeenCalledWith({
          fetchUserChatsAndChannelsPaginated: mockFetchUserChatsAndChannelsPaginated,
          fetchUserChatsTotalCount: expect.any(Function),
          getTeamsChatsApi: mockGetTeamsChatsApi,
          postTeamsChatsApi: mockPostTeamsChatsApi,
          deleteTeamsChatsApi: mockDeleteTeamsChatsApi,
          isModalOpen: true,
          openDB: mockOpenDB,
          eventReporter: mockEventReporter,
        });
      });
    });

    it('ローディング状態が正しく表示される', () => {
      mockUseUserChatsAndChannelsAccessor.mockReturnValue({
        fetchUserChatsAndChannelsPaginated: mockFetchUserChatsAndChannelsPaginated,
        fetchUserTeamsAndChannelsPaginated: undefined,
        fetchUserChatsTotalCount: jest.fn().mockResolvedValue(100),
        fetchUserTeamsTotalCount: undefined,
        isLoading: true,
        error: null,
      });

      render(<TeamsSettingModal {...defaultProps} />);

      expect(screen.getByText('チャットを読み込み中...')).toBeInTheDocument();
    });

    it('エラー状態が正しく表示される', () => {
      mockUseUserChatsAndChannelsAccessor.mockReturnValue({
        fetchUserChatsAndChannelsPaginated: mockFetchUserChatsAndChannelsPaginated,
        fetchUserTeamsAndChannelsPaginated: undefined,
        fetchUserChatsTotalCount: jest.fn().mockResolvedValue(100),
        fetchUserTeamsTotalCount: jest.fn().mockResolvedValue(50),
        isLoading: false,
        error: 'テストエラー',
      });

      render(<TeamsSettingModal {...defaultProps} />);

      expect(screen.getByText(/エラーが発生しました:/)).toBeInTheDocument();
      expect(screen.getByText(/テストエラー/)).toBeInTheDocument();
    });
  });

  // describe('タブ機能テスト', () => {
  //   it('タブの切り替えが正常に動作する', async () => {
  //     render(<TeamsSettingModal {...defaultProps} />);

  //     // データが読み込まれるまで待機
  //     await waitFor(() => {
  //       expect(screen.getByText('テストチャット1')).toBeInTheDocument();
  //     });

  //     // チャネルタブをクリック
  //     const channelTab = screen.getByText('チャネル (1)');
  //     fireEvent.click(channelTab);

  //     await waitFor(() => {
  //       expect(screen.getByText('テストチーム - 一般')).toBeInTheDocument();
  //       expect(screen.queryByText('テストチャット1')).not.toBeInTheDocument();
  //     });
  //   });

  //   it('タブ切り替え時に検索クエリがクリアされる', async () => {
  //     render(<TeamsSettingModal {...defaultProps} />);

  //     // データが読み込まれるまで待機
  //     await waitFor(() => {
  //       expect(screen.getByText('テストチャット1')).toBeInTheDocument();
  //     });

  //     // 検索入力
  //     const searchInput = screen.getByPlaceholderText('チャット名で検索');
  //     fireEvent.change(searchInput, { target: { value: 'テスト' } });

  //     // チャネルタブに切り替え
  //     const channelTab = screen.getByText('チャネル (1)');
  //     fireEvent.click(channelTab);

  //     // 検索フィールドがクリアされることを確認
  //     expect(searchInput).toHaveValue('');
  //   });
  // });

  describe('検索機能テスト', () => {
    beforeEach(() => {
      mockFetchUserChatsAndChannelsPaginated.mockResolvedValue({
        items: mockChatItems,
        hasMore: false,
        nextPageToken: undefined,
      });
    });

    it('検索入力でチャット名によるフィルタリングが動作する', async () => {
      render(<TeamsSettingModal {...defaultProps} />);

      // データが読み込まれるまで待機
      await waitFor(() => {
        expect(screen.getByText('テストチャット1')).toBeInTheDocument();
      });

      const searchInput = screen.getByPlaceholderText('チャット名で検索');
      fireEvent.change(searchInput, { target: { value: 'テスト' } });

      await waitFor(() => {
        expect(screen.getByText('テストチャット1')).toBeInTheDocument();
        expect(screen.queryByText('プロジェクトチャット')).not.toBeInTheDocument();
      });
    });

    it('検索入力でチャットIDによるフィルタリングが動作する', async () => {
      render(<TeamsSettingModal {...defaultProps} />);

      await waitFor(() => {
        expect(screen.getByText('テストチャット1')).toBeInTheDocument();
      });

      const searchInput = screen.getByPlaceholderText('チャット名で検索');
      fireEvent.change(searchInput, { target: { value: 'chat1' } });

      await waitFor(() => {
        expect(screen.getByText('テストチャット1')).toBeInTheDocument();
        expect(screen.queryByText('プロジェクトチャット')).not.toBeInTheDocument();
      });
    });

    it('検索結果が0件の場合、適切なメッセージが表示される', async () => {
      render(<TeamsSettingModal {...defaultProps} />);

      await waitFor(() => {
        expect(screen.getByText('テストチャット1')).toBeInTheDocument();
      });

      const searchInput = screen.getByPlaceholderText('チャット名で検索');
      fireEvent.change(searchInput, { target: { value: '存在しないチャット' } });

      await waitFor(() => {
        expect(screen.getByText('該当するチャットまたはチャネルが見つかりませんでした。')).toBeInTheDocument();
      });
    });
  });

  describe('選択機能テスト', () => {
    it('アイテムの選択と選択解除が正常に動作する', async () => {
      render(<TeamsSettingModal {...defaultProps} />);

      await waitFor(() => {
        expect(screen.getByText('テストチャット1')).toBeInTheDocument();
      });

      // アイテムをクリックして選択
      const chatItem = screen.getByText('テストチャット1').closest('[role="button"]');
      expect(chatItem).not.toBeNull();
      fireEvent.click(chatItem as HTMLElement);

      // 選択されたアイテムが表示されることを確認
      await waitFor(() => {
        expect(screen.getByText('Selected: chat1')).toBeInTheDocument();
      });

      // 再度クリックして選択解除
      fireEvent.click(chatItem as HTMLElement);

      await waitFor(() => {
        expect(screen.queryByText('Selected: chat1')).not.toBeInTheDocument();
      });
    });

    it('選択上限に達した場合、トースターメッセージが表示される', async () => {
      render(<TeamsSettingModal {...defaultProps} />);

      // 全てのチャットアイテムが表示されるまで待機
      await waitFor(() => {
        expect(screen.getByText('テストチャット1')).toBeInTheDocument();
        expect(screen.getByText('プロジェクトチャット')).toBeInTheDocument();
        expect(screen.getByText('テストチャット11')).toBeInTheDocument();
      });

      // 最初の10個のアイテムを選択して上限に達する
      const chatItemsToSelect = [
        'テストチャット1', 'プロジェクトチャット', 'テストチャット3', 'テストチャット4', 'テストチャット5',
        'テストチャット6', 'テストチャット7', 'テストチャット8', 'テストチャット9', 'テストチャット10',
      ];

      for (let i = 0; i < chatItemsToSelect.length; i += 1) {
        const chatName = chatItemsToSelect[i];
        const chatItem = screen.getByText(chatName).closest('[role="button"]');
        expect(chatItem).not.toBeNull();
        act(() => {
          fireEvent.click(chatItem as HTMLElement);
        });
      }

      // 11個目のアイテムを選択しようとする（上限は10個）
      const eleventhChatItem = screen.getByText('テストチャット11').closest('[role="button"]');
      expect(eleventhChatItem).not.toBeNull();

      act(() => {
        fireEvent.click(eleventhChatItem as HTMLElement);
      });

      // extendPopupTimerが呼ばれることを確認
      await waitFor(() => {
        expect(mockExtendPopupTimer).toHaveBeenCalledWith('max-teams-selection');
      });
    });

    it('キーボード操作（Enter/Space）で選択が動作する', async () => {
      render(<TeamsSettingModal {...defaultProps} />);

      await waitFor(() => {
        expect(screen.getByText('テストチャット1')).toBeInTheDocument();
      });

      const chatItem = screen.getByText('テストチャット1').closest('[role="button"]');
      expect(chatItem).not.toBeNull();

      // Enterキーで選択
      fireEvent.keyDown(chatItem as HTMLElement, { key: 'Enter' });

      await waitFor(() => {
        expect(screen.getByText('Selected: chat1')).toBeInTheDocument();
      });
    });
  });

  describe('ページネーション機能テスト', () => {
    it('さらに読み込むボタンが表示される', async () => {
      // hasMoreChatsがtrueの場合のモック設定
      mockUseTeamsSettingData.mockReturnValue({
        allChatItems: mockChatItems,
        savedItems: new Set(),
        savedSelectedItems: new Set(),
        isLoadingSavedItems: false,
        hasMoreChats: true,
        nextPageToken: 'next-token',
        isLoadingMore: false,
        isLoadingTotalCount: false,
        loadSavedItems: jest.fn(),
        saveSelectedItems: mockSaveSelectedItems,
        loadMoreChats: mockLoadMoreChats,
        setAllChatItems: jest.fn(),
        setSavedItems: mockSetSavedItems,
      });

      render(<TeamsSettingModal {...defaultProps} />);

      await waitFor(() => {
        expect(screen.getByText('テストチャット1')).toBeInTheDocument();
      });

      // さらに読み込むボタンが表示されることを確認
      expect(screen.getByText('さらに読み込む')).toBeInTheDocument();

      // 総件数が表示されることを確認
      expect(screen.getByText('11件表示中')).toBeInTheDocument();
    });

    it('さらに読み込むボタンをクリックするとloadMoreChatsが呼ばれる', async () => {
      // hasMoreChatsがtrueの場合のモック設定
      mockUseTeamsSettingData.mockReturnValue({
        allChatItems: mockChatItems,
        savedItems: new Set(),
        savedSelectedItems: new Set(),
        isLoadingSavedItems: false,
        hasMoreChats: true,
        hasMoreChannels: false,
        nextPageToken: 'next-token',
        nextChannelPageToken: undefined,
        isLoadingMore: false,
        isLoadingTotalCount: false,
        loadSavedItems: jest.fn(),
        saveSelectedItems: mockSaveSelectedItems,
        loadMoreChats: mockLoadMoreChats,
        loadMoreChannels: jest.fn(),
        setAllChatItems: jest.fn(),
        setSavedItems: mockSetSavedItems,
      });

      render(<TeamsSettingModal {...defaultProps} />);

      await waitFor(() => {
        expect(screen.getByText('さらに読み込む')).toBeInTheDocument();
      });

      // さらに読み込むボタンをクリック
      const loadMoreButton = screen.getByText('さらに読み込む');
      fireEvent.click(loadMoreButton);

      expect(mockLoadMoreChats).toHaveBeenCalled();
    });

    it('読み込み中はボタンが無効化される', async () => {
      // isLoadingMoreがtrueの場合のモック設定
      mockUseTeamsSettingData.mockReturnValue({
        allChatItems: mockChatItems,
        savedItems: new Set(),
        savedSelectedItems: new Set(),
        isLoadingSavedItems: false,
        hasMoreChats: true,
        nextPageToken: 'next-token',
        isLoadingMore: true,
        isLoadingTotalCount: false,
        loadSavedItems: jest.fn(),
        saveSelectedItems: mockSaveSelectedItems,
        loadMoreChats: mockLoadMoreChats,
        setAllChatItems: jest.fn(),
        setSavedItems: mockSetSavedItems,
      });

      render(<TeamsSettingModal {...defaultProps} />);

      await waitFor(() => {
        expect(screen.getByText('読み込み中...')).toBeInTheDocument();
      });

      // ボタンが無効化されていることを確認
      const loadMoreButton = screen.getByText('読み込み中...').closest('button');
      expect(loadMoreButton).toBeDisabled();
    });

    it('総件数取得中の表示が正しく動作する', async () => {
      // 総件数取得中の場合のモック設定
      mockUseTeamsSettingData.mockReturnValue({
        allChatItems: mockChatItems,
        savedItems: new Set(),
        savedSelectedItems: new Set(),
        isLoadingSavedItems: false,
        hasMoreChats: true,
        nextPageToken: 'next-token',
        isLoadingMore: false,
        isLoadingTotalCount: true,
        loadSavedItems: jest.fn(),
        saveSelectedItems: mockSaveSelectedItems,
        loadMoreChats: mockLoadMoreChats,
        setAllChatItems: jest.fn(),
        setSavedItems: mockSetSavedItems,
      });

      render(<TeamsSettingModal {...defaultProps} />);

      await waitFor(() => {
        expect(screen.getByText('テストチャット1')).toBeInTheDocument();
      });

      // 総件数取得中の表示を確認
      expect(screen.getByText('11件表示中 / 総件数取得中...')).toBeInTheDocument();
    });
  });

  describe('保存機能テスト', () => {
    it('保存ボタンが正常に動作する', async () => {
      render(<TeamsSettingModal {...defaultProps} />);

      await waitFor(() => {
        expect(screen.getByText('テストチャット1')).toBeInTheDocument();
      });

      // アイテムを選択
      const chatItem = screen.getByText('テストチャット1').closest('[role="button"]');
      expect(chatItem).not.toBeNull();
      fireEvent.click(chatItem as HTMLElement);

      await waitFor(() => {
        expect(screen.getByText('Selected: chat1')).toBeInTheDocument();
      });

      // 保存ボタンをクリック
      const saveButton = screen.getByText('保存');
      fireEvent.click(saveButton);

      await waitFor(() => {
        expect(mockSaveSelectedItems).toHaveBeenCalled();
      });
    });

    it('変更がない場合、保存ボタンが無効化される', async () => {
      render(<TeamsSettingModal {...defaultProps} />);

      await waitFor(() => {
        expect(screen.getByText('テストチャット1')).toBeInTheDocument();
      });

      // 保存ボタンが無効化されていることを確認
      const saveButton = screen.getByText('保存').closest('button');
      expect(saveButton).toBeDisabled();
    });

    it('保存中はローディング表示になる', async () => {
      // 保存処理を遅延させる
      mockSaveSelectedItems.mockImplementation(
        () => new Promise((resolve) => { setTimeout(resolve, 100); }),
      );

      render(<TeamsSettingModal {...defaultProps} />);

      await waitFor(() => {
        expect(screen.getByText('テストチャット1')).toBeInTheDocument();
      });

      // アイテムを選択
      const chatItem = screen.getByText('テストチャット1').closest('[role="button"]');
      expect(chatItem).not.toBeNull();
      fireEvent.click(chatItem as HTMLElement);

      // 保存ボタンをクリック
      const saveButton = screen.getByText('保存');
      fireEvent.click(saveButton);

      // ローディング表示を確認
      expect(screen.getByText('保存中...')).toBeInTheDocument();
    });
  });

  describe('閉じるボタンテスト', () => {
    it('PCの閉じるボタンをクリックするとonCloseが呼ばれる', () => {
      render(<TeamsSettingModal {...defaultProps} />);

      const closeButton = screen.getByRole('button', { name: /close/i });
      fireEvent.click(closeButton);

      expect(mockOnClose).toHaveBeenCalledTimes(1);
    });

    it('モーダルを閉じる時に状態がリセットされる', async () => {
      render(<TeamsSettingModal {...defaultProps} />);

      await waitFor(() => {
        expect(screen.getByText('テストチャット1')).toBeInTheDocument();
      });

      // アイテムを選択
      const chatItem = screen.getByText('テストチャット1').closest('[role="button"]');
      expect(chatItem).not.toBeNull();
      fireEvent.click(chatItem as HTMLElement);

      // 検索入力
      const searchInput = screen.getByPlaceholderText('チャット名で検索');
      fireEvent.change(searchInput, { target: { value: 'テスト' } });

      // 閉じるボタンをクリック
      const closeButton = screen.getByRole('button', { name: /close/i });
      fireEvent.click(closeButton);

      expect(mockOnClose).toHaveBeenCalled();
    });
  });

  // describe('プレースホルダーテキストテスト', () => {
  //   it('チャットタブではチャット用のプレースホルダーが表示される', async () => {
  //     render(<TeamsSettingModal {...defaultProps} />);

  //     await waitFor(() => {
  //       expect(screen.getByPlaceholderText('チャット名で検索')).toBeInTheDocument();
  //     });
  //   });

  //   it('チャネルタブではチャネル用のプレースホルダーが表示される', async () => {
  //     render(<TeamsSettingModal {...defaultProps} />);

  //     await waitFor(() => {
  //       expect(screen.getByText('テストチャット1')).toBeInTheDocument();
  //     });

  //     // チャネルタブに切り替え
  //     const channelTab = screen.getByText('チャネル (1)');
  //     fireEvent.click(channelTab);

  //     await waitFor(() => {
  //       expect(screen.getByPlaceholderText('チャネル名で検索')).toBeInTheDocument();
  //     });
  //   });
  // });
});
