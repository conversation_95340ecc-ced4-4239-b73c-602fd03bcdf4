import * as React from 'react';
import { EventReporter } from '@avanade-teams/app-insights-reporter';
import {
  IUserChatItem,
  FetchUserChatsAndChannelsPaginated,
  FetchUserChatsTotalCount,
  FetchUserTeamsTotalCount,
} from '../accessors/useUserChatsAndChannelsAccessor';
import { UseTeamsChatsApiReturnType } from '../accessors/useTeamsChatsApiAccessor';
import useTeamsChatsRepositoryAccessor from '../accessors/useTeamsChatsRepositoryAccessor';
import useRemoteTeamsChatsFeature from './useRemoteTeamsChatsFeature';
import { ITeamsChatsItem, DbProvider } from '../../types/IGeraniumAttaneDB';

// ISelectedItemの型定義
export interface ISelectedItem {
  id: string;
  name: string;
  type: 'チャット' | 'チャネル';
  chatType: 'oneOnOne' | 'group' | 'meeting' | 'channel';
  teamId?: string;
  countId?: number;
}

export interface UseTeamsSettingDataProps {
  fetchUserChatsAndChannelsPaginated?: FetchUserChatsAndChannelsPaginated;
  fetchUserChatsTotalCount?: FetchUserChatsTotalCount;
  fetchUserTeamsTotalCount?:FetchUserTeamsTotalCount;
  getTeamsChatsApi?: UseTeamsChatsApiReturnType['getTeamsChatsApi'];
  postTeamsChatsApi?: UseTeamsChatsApiReturnType['postTeamsChatsApi'];
  deleteTeamsChatsApi?: UseTeamsChatsApiReturnType['deleteTeamsChatsApi'];
  isModalOpen: boolean;
  openDB?: DbProvider;
  eventReporter: EventReporter;
}

export interface UseTeamsSettingDataReturnType {
  // データ状態
  allChatItems: IUserChatItem[];
  savedItems: Set<string>;
  savedSelectedItems: Set<ISelectedItem>;
  isLoadingSavedItems: boolean;
  // ページネーション（チャットとチャネル別々に管理）
  hasMoreChats: boolean;
  hasMoreChannels: boolean;
  nextPageToken?: string;
  nextChannelPageToken?: string;
  isLoadingMore: boolean;
  // 総件数
  totalChatCount?: number;
  totalChannelCount?: number;
  isLoadingTotalCount: boolean;
  // データ操作関数
  loadSavedItems: () => Promise<void>;
  saveSelectedItems: (selectedItems: Set<ISelectedItem>) => Promise<void>;
  loadMoreChats: () => Promise<void>;
  loadMoreChannels: () => Promise<void>;
  // 状態更新関数
  setAllChatItems: React.Dispatch<React.SetStateAction<IUserChatItem[]>>;
  setSavedItems: React.Dispatch<React.SetStateAction<Set<string>>>;
}

/**
 * Teams設定のデータ取得・保存ロジックを管理するカスタムフック
 */
const useTeamsSettingData = (props: UseTeamsSettingDataProps): UseTeamsSettingDataReturnType => {
  const {
    fetchUserChatsAndChannelsPaginated,
    fetchUserChatsTotalCount,
    fetchUserTeamsTotalCount,
    getTeamsChatsApi,
    postTeamsChatsApi,
    deleteTeamsChatsApi,
    isModalOpen,
    openDB,
    eventReporter,
  } = props;

  // リポジトリアクセサーとリモート機能を初期化
  const repositoryReturn = useTeamsChatsRepositoryAccessor(openDB);
  const apiReturn = { getTeamsChatsApi, postTeamsChatsApi, deleteTeamsChatsApi };
  const remoteFeature = useRemoteTeamsChatsFeature(repositoryReturn, apiReturn, eventReporter);

  const { addRemoteTeamsChats, deleteRemoteTeamsChats } = remoteFeature;

  // データ状態管理
  const [allChatItems, setAllChatItems] = React.useState<IUserChatItem[]>([]);
  const [savedItems, setSavedItems] = React.useState<Set<string>>(new Set());
  const [savedSelectedItems, setSavedSelectedItems] = React.useState<Set<ISelectedItem>>(new Set());
  const [isLoadingSavedItems, setIsLoadingSavedItems] = React.useState(false);

  // ページネーション状態管理（チャットとチャネル別々に管理）
  const [hasMoreChats, setHasMoreChats] = React.useState(false);
  const [hasMoreChannels, setHasMoreChannels] = React.useState(false);
  const [nextPageToken, setNextPageToken] = React.useState<string | undefined>(undefined);
  const [
    nextChannelPageToken,
    setNextChannelPageToken,
  ] = React.useState<string | undefined>(undefined);
  const [isLoadingMore, setIsLoadingMore] = React.useState(false);

  // 総件数
  const [totalChatCount, setTotalCount] = React.useState<number | undefined>(0);
  const [totalChannelCount, setTotalChannelCount] = React.useState<number | undefined>(0);
  const [isLoadingTotalCount, setIsLoadingTotalCount] = React.useState(false);

  // 保存済みアイテムを取得する関数
  const loadSavedItems = React.useCallback(async () => {
    if (!getTeamsChatsApi) return;

    setIsLoadingSavedItems(true);
    try {
      const savedTeamsChats = await getTeamsChatsApi();
      const savedItemIds = new Set(
        savedTeamsChats
          .map((item) => item.chatId || item.channelId)
          .filter((id): id is string => Boolean(id)),
      );
      setSavedItems(savedItemIds);

      // 名前付きの保存済みアイテムも作成
      const savedSelectedItemsArray = savedTeamsChats
        .map((item) => {
          const id = item.chatId || item.channelId;
          if (id && item.name) {
            return {
              id,
              name: item.name,
              type: item.chatType === 'channel' ? 'チャネル' : 'チャット',
              chatType: item.chatType as 'oneOnOne' | 'group' | 'meeting' | 'channel',
              teamId: item.teamId,
              countId: item.countId,
            } as ISelectedItem;
          }
          return null;
        })
        .filter((item): item is ISelectedItem => item !== null);

      setSavedSelectedItems(new Set(savedSelectedItemsArray));
    } catch (loadError) {
      throw new Error(`保存済みアイテムの取得に失敗しました: ${loadError}`);
    } finally {
      setIsLoadingSavedItems(false);
    }
  }, [getTeamsChatsApi]);

  // 選択されたアイテムを保存する関数
  const saveSelectedItems = React.useCallback(async (
    selectedItems: Set<ISelectedItem>,
  ) => {
    if (!addRemoteTeamsChats || !deleteRemoteTeamsChats) {
      throw new Error('リモート機能が利用できません');
    }

    // 削除対象のアイテムを特定（保存済みだが選択されていないアイテム）
    const selectedItemIds = new Set(Array.from(selectedItems).map((item) => item.id));
    // savedItemsはDBから直接取得しているでidしかない、しかし
    // selectedItemIdsはitem.nameがないと空になる
    // そこでsavedSelectedItemsからsavedItemsを消そうとすると見つからないのでエラーとなる。
    const itemsToDeleteNow = Array.from(savedItems).filter((id) => !selectedItemIds.has(id));

    // 1. 削除処理を先に実行
    if (itemsToDeleteNow.length > 0) {

      // 削除対象のTeamsChatsアイテムを作成
      const itemsToDelete = itemsToDeleteNow.map((chatId) => {
        // savedSelectedItemsから削除対象アイテムを検索
        const originalItem = Array.from(savedSelectedItems).find((item) => item.id === chatId);
        if (!originalItem) {
          throw new Error(`削除対象アイテムが見つかりません: ${chatId}`);
        }

        return {
          id: originalItem.id,
          name: originalItem.name,
          type: originalItem.type,
          chatType: originalItem.chatType,
          teamId: originalItem.teamId,
          countId: 0, // 削除時は不要
        } as ITeamsChatsItem;
      });

      // キューを使用して削除
      await Promise.all(
        itemsToDelete.map(async (item) => {
          try {
            await deleteRemoteTeamsChats(item);
          } catch (deleteError) {
            throw new Error(`Delete queue failed for item ${item.id}: ${deleteError}`);
          }
        }),
      );
    }

    // 2. 選択されたアイテムをすべてUpsert（新規追加または更新）
    if (selectedItems.size > 0) {
      // TeamsChatsアイテムに変換（countIdは順番に割り当て）
      const teamsChatsItems = Array.from(selectedItems).map((item, index) => ({
        id: item.id,
        name: item.name,
        type: item.type,
        chatType: item.chatType,
        teamId: item.teamId,
        countId: index + 1,
      } as ITeamsChatsItem));

      // キューを使用して追加
      await Promise.all(
        teamsChatsItems.map(async (item) => {
          try {
            await addRemoteTeamsChats(item);
          } catch (apiError) {
            throw new Error(`Add queue failed for item ${item.countId}: ${apiError}`);
          }
        }),
      );
    }

    // 保存済みアイテムを更新
    const updatedSelectedItemIds = new Set(Array.from(selectedItems).map((item) => item.id));
    setSavedItems(updatedSelectedItemIds);
    setSavedSelectedItems(new Set(selectedItems));
  }, [addRemoteTeamsChats, deleteRemoteTeamsChats, savedItems, savedSelectedItems]);

  // チャット用さらに読み込む関数
  const loadMoreChats = React.useCallback(async () => {
    if (!fetchUserChatsAndChannelsPaginated || !nextPageToken || isLoadingMore) {
      return;
    }

    setIsLoadingMore(true);
    try {
      const result = await fetchUserChatsAndChannelsPaginated(nextPageToken);
      // チャットのみを追加
      const chatItems = result.items.filter((item) => item.type === 'チャット');
      setAllChatItems((prev) => [...prev, ...chatItems]);

      // チャットのhasMoreを更新（チャットデータがある場合のみ）
      const hasMoreChatData = result.hasMore && chatItems.length > 0;
      setHasMoreChats(hasMoreChatData);
      setNextPageToken(result.nextPageToken);
    } catch {
      // エラー時は何もしない（現在の状態を維持）
    } finally {
      setIsLoadingMore(false);
    }
  }, [fetchUserChatsAndChannelsPaginated,
    nextPageToken,
    isLoadingMore]);

  // チャネル用さらに読み込む関数
  const loadMoreChannels = React.useCallback(async () => {
    if (!fetchUserChatsAndChannelsPaginated || !nextChannelPageToken || isLoadingMore) {
      return;
    }

    setIsLoadingMore(true);
    try {
      const result = await fetchUserChatsAndChannelsPaginated(nextChannelPageToken);
      // チャネルのみを追加
      const channelItems = result.items.filter((item) => item.type === 'チャネル');
      setAllChatItems((prev) => [...prev, ...channelItems]);

      // チャネルのhasMoreを更新（チャネルデータがある場合のみ）
      const hasMoreChannelData = result.hasMore && channelItems.length > 0;
      setHasMoreChannels(hasMoreChannelData);
      setNextChannelPageToken(result.nextPageToken);
    } catch {
      // エラー時は何もしない（現在の状態を維持）
    } finally {
      setIsLoadingMore(false);
    }
  }, [fetchUserChatsAndChannelsPaginated,
    nextChannelPageToken,
    isLoadingMore]);

  // データ取得のEffect（ページネーション対応）
  React.useEffect(() => {
    if (isModalOpen) {
      // ページネーション機能がある場合は初回20件のみ取得
      if (fetchUserChatsAndChannelsPaginated) {
        Promise.all([
          // 初回20件のチャット・チャネル一覧
          fetchUserChatsAndChannelsPaginated(),
          // 保存済みアイテム取得
          loadSavedItems(),
        ])
          .then(([result]) => {
            setAllChatItems(result.items);
            // チャットとチャネルの状態を別々に管理
            const chatItems = result.items.filter((item) => item.type === 'チャット');
            const channelItems = result.items.filter((item) => item.type === 'チャネル');

            // チャットのhasMoreは実際のチャットデータの有無で判定
            setHasMoreChats(result.hasMore && chatItems.length > 0);
            // チャネルのhasMoreは実際のチャネルデータの有無で判定
            setHasMoreChannels(result.hasMore && channelItems.length > 0);

            setNextPageToken(result.nextPageToken);
            setNextChannelPageToken(result.nextPageToken);
          })
          .catch(() => {
            // エラー時は空の状態を設定
            setAllChatItems([]);
            setHasMoreChats(false);
            setHasMoreChannels(false);
            setNextPageToken(undefined);
            setNextChannelPageToken(undefined);
          });
      }
    }
  }, [isModalOpen, fetchUserChatsAndChannelsPaginated, loadSavedItems]);

  // 総件数取得のEffect
  React.useEffect(() => {
    if (isModalOpen && fetchUserChatsTotalCount && fetchUserTeamsTotalCount) {
      setIsLoadingTotalCount(true);
      fetchUserChatsTotalCount()
        .then((chatsCount) => {
          setTotalCount(chatsCount);
        })
        .catch(() => {
          setTotalCount(undefined);
        })
        .finally(() => {
          setIsLoadingTotalCount(false);
        });
      fetchUserTeamsTotalCount()
        .then((channelCount) => {
          setTotalChannelCount(channelCount);
        })
        .catch(() => {
          setTotalChannelCount(undefined);
        })
        .finally(() => {
          setIsLoadingTotalCount(false);
        });
    }
  }, [isModalOpen, fetchUserChatsTotalCount, fetchUserTeamsTotalCount]);

  return {
    // データ状態
    allChatItems,
    savedItems,
    savedSelectedItems,
    isLoadingSavedItems,
    // ページネーション（チャットとチャネル別々に管理）
    hasMoreChats,
    hasMoreChannels,
    nextPageToken,
    nextChannelPageToken,
    isLoadingMore,
    // Chat総件数
    totalChatCount,
    // Channel総件数
    totalChannelCount,
    isLoadingTotalCount,

    // データ操作関数
    loadSavedItems,
    saveSelectedItems,
    loadMoreChats,
    loadMoreChannels,

    // 状態更新関数
    setAllChatItems,
    setSavedItems,
  };
};

export default useTeamsSettingData;
